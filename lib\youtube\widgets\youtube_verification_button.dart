import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:portraitmode/app/config/config.dart';

const List<String> scopes = [
  'https://www.googleapis.com/auth/youtube.readonly',
];

class YoutubeVerificationButton extends StatefulWidget {
  const YoutubeVerificationButton({super.key});

  @override
  YoutubeVerificationButtonState createState() =>
      YoutubeVerificationButtonState();
}

class YoutubeVerificationButtonState extends State<YoutubeVerificationButton> {
  late final GoogleSignIn _googleSignIn = GoogleSignIn.instance;
  bool googleSignIninitialized = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      // Add border and radius
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Youtube',
            style: TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16.0),
          const Text(
            'Connect your YouTube channel.',
            style: TextStyle(fontSize: 15.0, height: 1.5),
          ),
          const SizedBox(height: 10.0),
          const Text(
            'This will allow you to post your street-photography related YouTube videos on PortraitMode.',
            style: TextStyle(fontSize: 15.0, height: 1.5),
          ),
          const SizedBox(height: 22.0),
          SizedBox(
            width: double.infinity,
            height: 50.0,
            child: FilledButton(
              onPressed: _authenticate,
              style: FilledButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              child: const Text(
                'Connect your channel',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _authenticate() async {
    if (!googleSignIninitialized) {
      await _googleSignIn.initialize(clientId: GoogleConfig.oAuthClientId);
      googleSignIninitialized = true;
    }

    String msg = '';

    try {
      final GoogleSignInAccount signInAccount = await _googleSignIn
          .authenticate();

      msg = 'Signed in account: ${signInAccount.toString()}';
    } on PlatformException catch (error) {
      msg = 'Error when trying to sign in: ${error.toString()}';
    } catch (error) {
      msg = 'Error when trying to sign in: ${error.toString()}';
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(title: Text(msg));
        },
      );
    }
  }
}
