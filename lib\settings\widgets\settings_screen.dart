import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/artist/widgets/blocked_artists_screen.dart';
import 'package:portraitmode/auth/services/auth_service.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/moderation/widgets/review_featured_suggestions_screen.dart';
import 'package:portraitmode/moderation/widgets/review_reported_photos_screen.dart';
import 'package:portraitmode/onboarding/onboarding_screen.dart';
import 'package:portraitmode/photo/widgets/archived_photos_screen.dart';
import 'package:portraitmode/photo/widgets/liked_photos_screen.dart';
import 'package:portraitmode/profile/widgets/following_screen.dart';
import 'package:portraitmode/settings/widgets/app_version.dart';
import 'package:portraitmode/settings/widgets/delete_account_screen.dart';
import 'package:portraitmode/settings/widgets/settings_screen/settings_screen_list_tile.dart';
import 'package:portraitmode/settings/widgets/theme_screen.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  SettingsScreenState createState() => SettingsScreenState();
}

class SettingsScreenState extends ConsumerState<SettingsScreen> {
  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final String role = LocalUserService.role ?? 'subscriber';
    final bool allowModeration = role == 'administrator' || role == 'editor';

    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            PmSliverAppBar(
              scrollController: _scrollController,
              titleText: 'Settings',
              automaticallyImplyLeading: true,
              useLogo: false,
              actions: const [],
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.only(left: 3.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: LayoutConfig.contentTopGap),
                    if (role == 'administrator')
                      SettingsScreenListTile(
                        title: 'Welcome',
                        icon: Ionicons.star_outline,
                        onTap: () {
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const OnboardingScreen(isLoggedIn: true),
                            ),
                          );
                        },
                      ),
                    if (role == 'administrator') const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Liked photos',
                      icon: Ionicons.heart_outline,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LikedPhotosScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Archived photos',
                      icon: Ionicons.archive_outline,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ArchivedPhotosScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Following',
                      icon: Ionicons.person_add_outline,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FollowingScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Blocked',
                      icon: Icons.block,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const BlockedArtistsScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Theme',
                      icon: Ionicons.color_palette_outline,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ThemeScreen(),
                          ),
                        );
                      },
                    ),
                    if (allowModeration)
                      Padding(
                        padding: const EdgeInsets.only(
                          right: ScreenStyleConfig.horizontalPadding,
                          left: ScreenStyleConfig.horizontalPadding,
                          bottom: 8.0,
                          top: 12.0,
                        ),
                        child: Divider(
                          height: 1.0,
                          color: context.colors.borderColor,
                        ),
                      ),
                    if (allowModeration)
                      SettingsScreenListTile(
                        title: 'Reported photos',
                        icon: Ionicons.notifications_outline,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const ReviewReportedPhotosScreen(),
                            ),
                          );
                        },
                      ),
                    if (allowModeration) const SizedBox(height: 5.0),
                    if (allowModeration)
                      SettingsScreenListTile(
                        title: 'Featured suggestions',
                        icon: Ionicons.trophy_outline,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const ReviewFeaturedSuggestionsScreen(),
                            ),
                          );
                        },
                      ),
                    if (allowModeration) const SizedBox(height: 5.0),
                    if (allowModeration)
                      SettingsScreenListTile(
                        title: 'Category suggestions',
                        icon: Ionicons.bookmark_outline,
                        onTap: () {
                          // Navigator.push(
                          //   context,
                          //   MaterialPageRoute(
                          //     builder: (context) => const ThemeScreen(),
                          //   ),
                          // );
                        },
                      ),
                    if (allowModeration)
                      Padding(
                        padding: const EdgeInsets.only(
                          right: ScreenStyleConfig.horizontalPadding,
                          left: ScreenStyleConfig.horizontalPadding,
                          bottom: 8.0,
                          top: 12.0,
                        ),
                        child: Divider(
                          height: 1.0,
                          color: context.colors.borderColor,
                        ),
                      ),
                    if (role != 'administrator') const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Log out',
                      icon: Ionicons.log_out_outline,
                      onTap: () async {
                        await AuthService.logout(context, ref);
                      },
                    ),
                    if (role != 'administrator') const SizedBox(height: 5.0),
                    SettingsScreenListTile(
                      title: 'Delete account',
                      icon: Ionicons.trash_outline,
                      isDanger: true,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DeleteAccountScreen(),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 20.0),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 18.0),
                      child: AppVersion(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
