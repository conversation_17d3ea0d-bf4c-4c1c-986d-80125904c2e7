import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/photo/http_responses/like_photo_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/like_photo_service.dart';

class LikeIconButton extends ConsumerStatefulWidget {
  const LikeIconButton({
    super.key,
    required this.photoId,
    this.isLiked = false,
    this.totalLikes = 0,
    this.showCount = false,
    this.showText = false,
    this.fontSize = 11.0,
    this.iconSize = 25.0,
    this.iconToTextGap = 4.0,
    this.shrinkTapTarget = false,
  });

  final int photoId;
  final bool isLiked;
  final int totalLikes;
  final bool showCount;
  final bool showText;
  final double fontSize;
  final double iconSize;
  final double iconToTextGap;
  final bool shrinkTapTarget;

  @override
  LikeIconButtonState createState() => LikeIconButtonState();
}

class LikeIconButtonState extends ConsumerState<LikeIconButton> {
  final _likePhotoService = LikePhotoService();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Color textColor = context.colors.brandColor;

    log('LikeIconButton.isLiked: ${widget.isLiked}');

    return Row(
      children: [
        IconButton(
          padding: const EdgeInsets.all(0.0),
          icon: Icon(
            (widget.isLiked ? Ionicons.heart : Ionicons.heart_outline),
          ),
          iconSize: widget.iconSize,
          color: (widget.isLiked == true
              ? context.colors.accentColor
              : context.colors.iconColor),
          style: widget.shrinkTapTarget
              ? IconButton.styleFrom(
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                )
              : null,
          onPressed: () {
            doAction(widget.isLiked ? 'unlike' : 'like');
          },
        ),
        if (widget.showCount) SizedBox(width: widget.iconToTextGap),
        if (widget.showCount)
          Text(
            widget.totalLikes.toString(),
            style: TextStyle(
              color: textColor,
              fontSize: widget.fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        if (widget.showCount && widget.showText) const SizedBox(width: 3.0),
        if (widget.showCount && widget.showText)
          Text(
            widget.totalLikes > 1 ? 'Likes' : 'Like',
            style: TextStyle(
              color: textColor,
              fontSize: widget.fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
      ],
    );
  }

  void doAction(String actionType) async {
    // Switch the isLiked status even though the request hasn't been made.
    ref
        .read(photoStoreProvider.notifier)
        .setIsLiked(widget.photoId, actionType == 'like');

    LikePhotoResponse response = actionType == 'like'
        ? await _likePhotoService.like(widget.photoId)
        : await _likePhotoService.unlike(widget.photoId);

    if (!response.success) {
      // If failed, switch the isLiked status back to the previous state.
      ref
          .read(photoStoreProvider.notifier)
          .setIsLiked(widget.photoId, actionType != 'like');

      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .setTotalLikes(widget.photoId, response.data!.totalLikes);
  }
}
