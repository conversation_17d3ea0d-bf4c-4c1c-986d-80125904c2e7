import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/utils/content_util.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/providers/camera_photos_provider.dart';
import 'package:portraitmode/category/providers/category_photos_provider.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/archived_photos_provider.dart';
import 'package:portraitmode/photo/providers/featured_photos_provider.dart';
import 'package:portraitmode/photo/providers/following_photos_provider.dart';
import 'package:portraitmode/photo/providers/latest_photos_provider.dart';
import 'package:portraitmode/photo/services/photo_archive_service.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/search/providers/search_photos_provider.dart';

class ArchiveAssignmentUtil {
  final WidgetRef ref;
  final PhotoData photo;
  final VoidCallback? startLoading;
  final Function({void Function()? callback})? stopLoading;
  final Function(String)? onSuccess;
  final VoidCallback? onSessionEndedError;
  final Function(String)? onError;

  ArchiveAssignmentUtil({
    required this.ref,
    required this.photo,
    this.startLoading,
    this.stopLoading,
    this.onSuccess,
    this.onSessionEndedError,
    this.onError,
  });

  Future<void> handleAssignment({
    required AssignmentActionType actionType,
  }) async {
    startLoading?.call();

    final photoArchiveService = PhotoArchiveService();

    final response = await photoArchiveService.handleAssignment(
      photoId: photo.id,
      actionType: actionType,
    );

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        onSessionEndedError?.call();
      } else {
        onError?.call(response.message);
      }

      stopLoading?.call();
      return;
    }

    stopLoading?.call(
      callback: () {
        if (actionType == AssignmentActionType.assign) {
          _visuallyArchivePhoto();
        } else {
          _visuallyUnarchivePhoto();
        }

        onSuccess?.call(response.message);
      },
    );
  }

  void _visuallyArchivePhoto() {
    ref.read(latestPhotoIdsProvider.notifier).removeItem(photo.id);
    ref.read(followingPhotoIdsProvider.notifier).removeItem(photo.id);

    ref.read(featuredPhotoIdsProvider.notifier).removeItem(photo.id);
    ref.read(searchPhotoIdsProvider.notifier).removeItem(photo.id);

    ref.read(categoryPhotoIdsProvider.notifier).removeIdFromAllKeys(photo.id);
    ref.read(cameraPhotoIdsProvider.notifier).removeIdFromAllKeys(photo.id);

    ref.read(myAlbumProvider.notifier).decrementTotalPhotos(photo.album);

    // Loop through global myAlbumPhotoListProviderMap object using foreach.
    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
    ) {
      ref.read(provider.notifier).removeItem(photo.id);
    });
  }

  void _visuallyUnarchivePhoto() {
    ref.read(archivedPhotoIdsProvider.notifier).removeItem(photo.id);

    ref.read(latestPhotoIdsProvider.notifier).addItem(photo.id, reorder: true);

    ref
        .read(followingPhotoIdsProvider.notifier)
        .addItem(photo.id, reorder: true);

    if (photo.featured) {
      ref
          .read(featuredPhotoIdsProvider.notifier)
          .addItem(photo.id, reorder: true);
    }

    if (photo.categories.isNotEmpty) {
      for (int categoryId in photo.categories) {
        ref
            .read(categoryPhotoIdsProvider.notifier)
            .addItem(categoryId, photo.id, reorder: true);
      }
    }

    if (photo.camera.isNotEmpty) {
      final cameraSlug = slugify(photo.camera);

      ref
          .read(cameraPhotoIdsProvider.notifier)
          .addItem(cameraSlug, photo.id, reorder: true);
    }

    ref.read(myAlbumProvider.notifier).incrementTotalPhotos(photo.album);

    // Loop through global myAlbumPhotoListProviderMap object using foreach.
    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
    ) {
      if (photo.album == albumSlug || albumSlug == 'all-photos') {
        // Add matched photo to current iterated album's photo list.
        ref.read(provider.notifier).addItemThenReorder(photo);
      }
    });
  }
}
