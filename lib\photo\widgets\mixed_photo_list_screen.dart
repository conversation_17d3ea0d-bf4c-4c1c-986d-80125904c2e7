import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/dto/initial_data.dart';
import 'package:portraitmode/app/http_responses/initial_data_response.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/services/app_service.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/artist/dto/simple_artist_data.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_list_slider.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/photo/dto/mixed_feeed_sub_arg_data.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/latest_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/photo/widgets/trending_photo_list_slider.dart';

class MixedPhotoListScreen extends ConsumerStatefulWidget {
  const MixedPhotoListScreen({super.key, this.onInit});

  final Function(Function)? onInit;

  @override
  MixedPhotoListScreenState createState() => MixedPhotoListScreenState();
}

class MixedPhotoListScreenState extends ConsumerState<MixedPhotoListScreen> {
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final int _latestPhotosPerPage = (LoadMoreConfig.photosPerPage / 2).round();
  final int _followedPhotosPerPage = (LoadMoreConfig.photosPerPage / 2).round();
  List<int> _currentlyViewedPhotoIds = [];

  int _currentPageNumber = 0;
  bool _loadMoreEndReached = false;
  List<PhotoData> _trendingPhotoList = [];

  bool _initialDataFetched = false;
  List<CategoryData> _unfollowedCategoryList = [];

  final _appService = AppService();
  final _photoListService = PhotoListService();
  final _categoryListService = CategoryListService();

  @override
  void initState() {
    widget.onInit?.call(_scrollToTop);
    _profileId = LocalUserService.userId ?? 0;
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Consumer(
      builder: (context, ref, child) {
        bool behaveNewlyOpened = ref.watch(
          appStateProvider.select((data) => data.behaveNewlyOpened),
        );

        if (behaveNewlyOpened) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToTop();

            if (mounted) {
              // Reset the behaveNewlyOpened flag after handling it.
              ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
            }
          });
        }

        return _buildScreen();
      },
    );
  }

  Widget _buildScreen() {
    List<PhotoData> photoList = ref.watch(latestPhotosProvider);

    return Scaffold(
      body: SafeArea(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _handleRefresh,
            child: ValueListenableBuilder(
              valueListenable: _blockScrollNotifier,
              builder: (context, blockScrolling, child) {
                return CustomScrollView(
                  controller: _scrollController,
                  physics: blockScrolling
                      ? const NeverScrollableScrollPhysics()
                      : null,
                  cacheExtent: _cacheExtent,
                  slivers: <Widget>[
                    PmSliverAppBar(scrollController: _scrollController),
                    EasyLoadMore(
                      isFinished: _loadMoreEndReached,
                      onLoadMore: _handleLoadMore,
                      loadingWidgetColor: context.colors.baseColorAlt,
                      runOnEmptyResult: true,
                      idleStatusText: "",
                      loadingStatusText: "",
                      finishedStatusText: "",
                      child: _buildSliverListView(photoList),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView(List<PhotoData> photoList) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        if (index >= photoList.length) {
          return const SizedBox.shrink();
        }

        double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
        bool isOwnProfile = photoList[index].authorId == _profileId;

        return Container(
          margin: EdgeInsets.only(top: marginTop),
          child: (index == 9 || index == 18
              ? Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20.0),
                      child: (index == 9
                          ? CategoryListSlider(
                              categoryList: _unfollowedCategoryList,
                              padding: const EdgeInsets.only(
                                top: 20.0,
                                bottom: 22.0,
                              ),
                              decoration: BoxDecoration(
                                border: Border(
                                  top: BorderSide(
                                    color: context.colors.borderColor,
                                    width: 1.0,
                                  ),
                                  bottom: BorderSide(
                                    color: context.colors.borderColor,
                                    width: 1.0,
                                  ),
                                ),
                              ),
                            )
                          : TrendingPhotoListSlider(
                              photoList: _trendingPhotoList,
                            )),
                    ),
                    PhotoListItem(
                      index: index,
                      photo: photoList[index],
                      isOwnProfile: isOwnProfile,
                      screenName: 'explore_screen',
                      onTwoFingersOn: () {
                        _blockScrollNotifier.value = true;
                      },
                      onTwoFingersOff: () {
                        _blockScrollNotifier.value = false;
                      },
                    ),
                  ],
                )
              : PhotoListItem(
                  index: index,
                  photo: photoList[index],
                  isOwnProfile: isOwnProfile,
                  screenName: 'explore_screen',
                  onTwoFingersOn: () {
                    _blockScrollNotifier.value = true;
                  },
                  onTwoFingersOff: () {
                    _blockScrollNotifier.value = false;
                  },
                )),
        );
      }, childCount: photoList.length),
    );
  }

  void _scrollToTop() async {
    scrollListToTop(_scrollController, _refreshIndicatorKey);
  }

  Future<void> _handleRefresh() async {
    _loadMoreEndReached = false;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetchMixed(
        latestArg: MixedFeedSubArgData(limit: _latestPhotosPerPage),
        fromFollowedArtistsArg: MixedFeedSubArgData(
          limit: _followedPhotosPerPage,
        ),
      ),
      _categoryListService.fetchUnfollowed(limit: 24),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    final PhotoListResponse photoListResponse = reponses[0];
    final CategoryListResponse categoryListResponse = reponses[1];

    if (categoryListResponse.success) {
      _unfollowedCategoryList = categoryListResponse.data;
    }

    _handlePhotoListResponse(photoListResponse, true, false);
  }

  Future<bool> _handleLoadMore() async {
    List<dynamic> responses = [];
    late PhotoListResponse photoListResponse;
    late PhotoListResponse trendingPhotoListResponse;
    bool shouldUpdateTrendingPhotos = false;

    final List<int> viewedPhotoIds = _currentPageNumber > 0
        ? _currentlyViewedPhotoIds
        : [];

    // log('The _currentPageNumber are: $_currentPageNumber');

    if (_currentPageNumber == 1) {
      shouldUpdateTrendingPhotos = true;

      responses = await Future.wait([
        _photoListService.fetchMixed(
          latestArg: MixedFeedSubArgData(limit: _latestPhotosPerPage),
          fromFollowedArtistsArg: MixedFeedSubArgData(
            limit: _followedPhotosPerPage,
          ),
          viewedPhotoIds: viewedPhotoIds,
        ),
        _photoListService.fetchTrending(limit: 24),
      ]);

      photoListResponse = responses[0];
      trendingPhotoListResponse = responses[1];
    } else {
      if (_currentPageNumber == 0 && !_initialDataFetched) {
        late InitialDataResponse initialDataResponse;

        responses = await Future.wait([
          _photoListService.fetchMixed(
            latestArg: MixedFeedSubArgData(limit: _latestPhotosPerPage),
            fromFollowedArtistsArg: MixedFeedSubArgData(
              limit: _followedPhotosPerPage,
            ),
            viewedPhotoIds: viewedPhotoIds,
          ),
          _appService.fetchInitialData(),
        ]);

        photoListResponse = responses[0];
        initialDataResponse = responses[1];

        // log('The initialDataResponse in home screen is: ${initialDataResponse.data.toString()}');

        if (initialDataResponse.success && initialDataResponse.data != null) {
          final InitialData initialData = initialDataResponse.data!;

          // log('The simpleArtistData is: ${initialData.simpleArtistData.toString()}');

          if (initialData.simpleArtistData != null) {
            SimpleArtistData simpleArtistData = initialData.simpleArtistData!;

            // log('And the role is: ${simpleArtistData.role}');

            await LocalUserService.replace(
              LocalUserData(
                userId: simpleArtistData.id,
                nicename: simpleArtistData.nicename,
                role: simpleArtistData.role,
                displayName: simpleArtistData.displayName,
                profileUrl: simpleArtistData.profileUrl,
                avatarUrl: simpleArtistData.avatarUrl,
                membershipType: simpleArtistData.membershipType,
              ),
            );
          }

          _unfollowedCategoryList = initialData.unfollowedCategoryList;

          if (initialData.notificationFetchResult != null) {
            ref
                .read(notificationProvider.notifier)
                .replace(initialData.notificationFetchResult!);
          }

          globalFeedbackTokensAmount = initialData.feedbackTokensAmount;

          _initialDataFetched = true;
        }
      } else {
        photoListResponse = await _photoListService.fetchMixed(
          latestArg: MixedFeedSubArgData(limit: _latestPhotosPerPage),
          fromFollowedArtistsArg: MixedFeedSubArgData(
            limit: _followedPhotosPerPage,
          ),
          viewedPhotoIds: viewedPhotoIds,
        );
      }
    }

    final isFirstLoad = _currentPageNumber == 0;

    _handlePhotoListResponse(photoListResponse, false, isFirstLoad);

    if (shouldUpdateTrendingPhotos &&
        mounted &&
        trendingPhotoListResponse.success &&
        trendingPhotoListResponse.data.isNotEmpty) {
      setState(() {
        _trendingPhotoList = trendingPhotoListResponse.data;
      });
    }

    return photoListResponse.success;
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      _currentlyViewedPhotoIds = [];

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      _currentlyViewedPhotoIds = [];

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Update the list of currently viewed photo ids.
    _currentlyViewedPhotoIds = response.data.map((photo) => photo.id).toList();

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(latestPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      if (mounted) {
        setState(() {
          _currentPageNumber = 1;
        });
      }

      return;
    }

    if (isFirstLoad) {
      ref
          .read(latestPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      if (mounted) {
        setState(() {
          _currentPageNumber = 1;
        });
      }

      return;
    }

    ref
        .read(latestPhotoIdsProvider.notifier)
        .addItems(photoListToIdList(response.data));

    if (mounted) {
      setState(() {
        _currentPageNumber++;
      });
    }
  }
}
