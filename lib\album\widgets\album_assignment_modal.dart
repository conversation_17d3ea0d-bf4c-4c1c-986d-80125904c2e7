import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/http_responses/album_list_response.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/services/photo_service.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class AlbumAssignmentModal extends ConsumerStatefulWidget {
  const AlbumAssignmentModal({
    super.key,
    required this.scrollController,
    required this.photo,
    this.onSaved,
  });

  final ScrollController scrollController;
  final PhotoData photo;
  final Function(String)? onSaved;

  @override
  AlbumAssignmentModalState createState() => AlbumAssignmentModalState();
}

class AlbumAssignmentModalState extends ConsumerState<AlbumAssignmentModal> {
  final _photoService = PhotoService();
  String _selectedAlbumSlug = '';
  bool _isLoading = false;

  @override
  void initState() {
    _selectedAlbumSlug = widget.photo.album;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<AlbumData> albums = ref.watch(myAlbumProvider);

    return SafeArea(
      child: Column(
        children: [
          Expanded(
            child: ListView(
              controller: widget.scrollController,
              children: [
                const ModalDragHandle(),
                const SizedBox(height: 8),
                for (AlbumData album in albums)
                  if (album.slug != 'all-photos')
                    ListTile(
                      title: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            album.text,
                            style: const TextStyle(fontSize: 14.5),
                          ),
                          if (_selectedAlbumSlug == album.slug)
                            const SizedBox(width: 5.0),
                          if (_selectedAlbumSlug == album.slug)
                            Icon(
                              Ionicons.checkmark_circle_sharp,
                              color: context.colors.accentColor,
                              size: 15,
                            ),
                        ],
                      ),
                      onTap: () => _handleOnTap(album.slug),
                      // dense: true,
                      visualDensity: const VisualDensity(vertical: -3.0),
                    ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8.0,
              bottom: 14.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: SubmitButton(
              buttonText: "Update",
              width: double.infinity,
              height: 40.0,
              fontWeight: FontWeight.w600,
              onPressed: () async {
                await _handleOnSubmit(_selectedAlbumSlug);
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleOnTap(String targettedAlbumSlug) async {
    if (!mounted) return;

    setState(() {
      if (_selectedAlbumSlug == targettedAlbumSlug) {
        _selectedAlbumSlug = '';
      } else {
        _selectedAlbumSlug = targettedAlbumSlug;
      }
    });
  }

  Future<void> _handleOnSubmit(String targettedAlbumSlug) async {
    if (_isLoading) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    AlbumListResponse response = await _photoService.assignToAlbum(
      photoId: widget.photo.id,
      albumSlug: targettedAlbumSlug,
    );

    if (!response.success) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    // The albums & their counts are from server side.
    ref.read(myAlbumProvider.notifier).replaceAll(response.data);

    // But the photo update is from client side which I think is fine for now.
    PhotoData updatedPhoto = widget.photo.copyWith(album: targettedAlbumSlug);

    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
    ) {
      if (albumSlug == 'all-photos') {
        ref.read(provider.notifier).updateItem(updatedPhoto);
      } else {
        if (albumSlug == widget.photo.album) {
          ref.read(provider.notifier).removeItem(widget.photo.id);
        } else if (albumSlug == targettedAlbumSlug) {
          ref.read(provider.notifier).addItemThenReorder(updatedPhoto);
        }
      }
    });

    // Only pop if the modal is still open.
    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      Navigator.of(context).pop();
    }

    widget.onSaved?.call(response.message);
  }
}
