// photos_tab_content.dart

import 'dart:developer';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/providers/search_photos_data_provider.dart';
import 'package:portraitmode/search/providers/search_photos_provider.dart';

class PhotosTabContent extends ConsumerStatefulWidget {
  const PhotosTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
    this.onPhotoItemTwoFingersOn,
    this.onPhotoItemTwoFingersOff,
  });

  final PhotosSearchData searchData;
  final String keyword;
  final List<PhotoData> dataList;
  final Function? onPhotoItemTwoFingersOn;
  final Function? onPhotoItemTwoFingersOff;

  @override
  PhotosTabContentState createState() => PhotosTabContentState();
}

class PhotosTabContentState extends ConsumerState<PhotosTabContent> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final _photoListService = PhotoListService();
  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  bool _isLoadingMore = false;

  double? _loadMoreThreshold;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  double _getLoadMoreThreshold() {
    if (_loadMoreThreshold == null) {
      final screenHeight = MediaQuery.of(context).size.height;
      final dynamicThreshold = screenHeight * LoadMoreConfig.tresholdByScreen;

      // Add bounds by min and max.
      _loadMoreThreshold = math.max(
        math.min(dynamicThreshold, LoadMoreConfig.maxTreshold),
        LoadMoreConfig.minTreshold,
      );
    }
    return _loadMoreThreshold!;
  }

  bool _canLoadMore() {
    return !_isLoadingMore && !widget.searchData.loadMoreEndReached;
  }

  void _triggerLoadMore() async {
    if (_isLoadingMore || widget.searchData.loadMoreEndReached) return;

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    // log('build screen: PhotosTabContent');

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _handleRefresh,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            // Handle load more when scrolling reaches the threshold
            if (scrollInfo.metrics.pixels >=
                scrollInfo.metrics.maxScrollExtent - _getLoadMoreThreshold()) {
              if (_canLoadMore()) {
                _triggerLoadMore();
              }
            }
            return false; // Allow the notification to continue
          },
          child: MasonryGridView.count(
            // Remove the controller - let NestedScrollView handle scrolling
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
            crossAxisCount: 2,
            mainAxisSpacing: 8.0,
            crossAxisSpacing: 8.0,
            cacheExtent: _cacheExtent,
            itemCount:
                widget.dataList.length +
                (_isLoadingMore && !widget.searchData.loadMoreEndReached
                    ? 1
                    : 0),
            itemBuilder: (BuildContext context, int index) {
              // Handle loading indicator as the last item
              if (index == widget.dataList.length) {
                return Container(
                  padding: const EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: CircularProgressIndicator(
                    color: context.colors.baseColorAlt,
                  ),
                );
              }

              if (index >= widget.dataList.length) {
                return const SizedBox.shrink();
              }

              return PhotoMasonryItem(
                key: ValueKey(widget.dataList[index].id),
                index: index,
                photo: widget.dataList[index],
                isOwnProfile: widget.dataList[index].authorId == _profileId,
                screenName: 'photo_search_screen',
                onTwoFingersOn: () {
                  if (!mounted) return;

                  if (widget.onPhotoItemTwoFingersOn != null) {
                    widget.onPhotoItemTwoFingersOn!();
                  }
                },
                onTwoFingersOff: () {
                  if (!mounted) return;

                  if (widget.onPhotoItemTwoFingersOff != null) {
                    widget.onPhotoItemTwoFingersOff!();
                  }
                },
                onPhotoTap: () => _handlePhotoTap(widget.dataList[index]),
              );
            },
          ),
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoDetailScreen(
          photo: photo,
          originScreenName: 'photo_search_screen',
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // Reset loading state
    _isLoadingMore = false;

    late PhotoListResponse response;

    // log('The photos search keyword onRefresh is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _photoListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: 0,
      );
    } else {
      response = await _photoListService.fetch(
        limit: _loadMorePerPage,
        lastId: 0,
      );
    }

    _handlePhotoListResponse(response, true, false);
  }

  Future<void> _handleLoadMore() async {
    late PhotoListResponse response;

    log('The photos search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _photoListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: widget.searchData.loadMoreLastId,
      );
    } else {
      response = await _photoListService.fetch(
        limit: _loadMorePerPage,
        lastId: widget.searchData.loadMoreLastId,
      );
    }

    final isFirstLoad = widget.searchData.loadMoreLastId == 0;

    _handlePhotoListResponse(response, false, isFirstLoad);
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchPhotosReactiveService = ref.read(
      searchPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        searchPhotosReactiveService.clear();
      }

      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    ref
        .read(searchPhotosDataProvider.notifier)
        .setLoadMoreLastId(response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      searchPhotosReactiveService.replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        searchPhotosReactiveService.replaceAll(response.data);
      } else {
        searchPhotosReactiveService.addItems(response.data);
      }
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(false);
    }
  }
}
