import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/http_responses/album_list_response.dart';
import 'package:portraitmode/album/services/album_service.dart';
import 'package:portraitmode/album/widgets/edit_album_modal.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class AlbumTabbarItem extends ConsumerStatefulWidget {
  const AlbumTabbarItem({
    super.key,
    required this.tabbarContext,
    required this.index,
    required this.album,
    this.isLastItem = false,
  });

  final BuildContext tabbarContext;
  final int index;
  final AlbumData album;
  final bool isLastItem;

  @override
  AlbumTabbarItemState createState() => AlbumTabbarItemState();
}

class AlbumTabbarItemState extends ConsumerState<AlbumTabbarItem> {
  bool _isLoading = false;

  TabController? tabController;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timestamp) {
      if (globalMyAlbumSwitchToLastTab && widget.isLastItem) {
        globalMyAlbumSwitchToLastTab = false;

        tabController = DefaultTabController.of(widget.tabbarContext);

        tabController?.animateTo(widget.index);
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String tabText = widget.album.totalPhotos > 0
        ? '${widget.album.text} (${widget.album.totalPhotos})'
        : widget.album.text;

    // Generate spaces with chars count is the same as tabText chars count.
    String spaceChars = '';
    for (int i = 0; i < tabText.length; i++) {
      spaceChars += ' ';
    }

    return Container(
      color: context.colors.lightColor,
      child: widget.album.slug == 'all-photos'
          ? Tab(text: tabText)
          : _isLoading
          ? Tab(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Tab(text: spaceChars),
                  _buildLoadingWidget(),
                ],
              ),
            )
          : GestureDetector(
              onLongPress: () => _handleOnLongPress(),
              child: Tab(text: tabText),
            ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: SizedBox(
        width: 20.0,
        height: 20.0,
        child: CircularProgressIndicator(
          color: context.colors.baseColor,
          strokeWidth: 2.0,
        ),
      ),
    );
  }

  void _handleOnLongPress() {
    HapticFeedback.vibrate();

    showModalBottomSheet(
      context: context,
      // isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.only(
            top: 8.0,
            bottom: MediaQuery.viewInsetsOf(context).bottom,
          ),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
          ),
          height: 98.0,
          child: Column(
            children: [
              ModalListTile(
                title: "Edit album",
                iconData: Ionicons.create_outline,
                onTap: () => _showEditAlbumModal(),
              ),
              ModalListTile(
                title: "Delete album",
                iconColor: context.colors.dangerColor,
                textColor: context.colors.dangerColor,
                iconData: Ionicons.trash_outline,
                onTap: () => showDialog(
                  context: context,
                  builder: (BuildContext context) => AlertDialog(
                    title: const Text('Delete album'),
                    content: Text(
                      'Are you sure you want to delete "${widget.album.text}" album?',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () async {
                          Navigator.pop(context);
                          await _handleDeleteAlbum();
                        },
                        child: Text(
                          'Delete',
                          style: TextStyle(color: context.colors.dangerColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Container(
              //   height: 1.0,
              //   color: Colors.blue,
              // ),
            ],
          ),
        );
      },
    );
  }

  void _showEditAlbumModal() {
    Navigator.pop(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return EditAlbumModal(album: widget.album);
      },
    );
  }

  Future<void> _handleDeleteAlbum() async {
    if (mounted) {
      Navigator.pop(context);

      setState(() {
        _isLoading = true;
      });
    }

    final albumService = AlbumService();

    AlbumListResponse response = await albumService.delete(widget.album.slug);

    if (!response.success) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }

    ref.read(myAlbumProvider.notifier).replaceAll(response.data);
    tabController?.animateTo(0);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );
    }
  }

  @override
  void dispose() {
    tabController?.dispose();
    super.dispose();
  }
}
