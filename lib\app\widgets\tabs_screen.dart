import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/home/<USER>/home_screen.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/notification/utils/notification_util.dart';
import 'package:portraitmode/notification/widgets/notif_icon.dart';
import 'package:portraitmode/notification/widgets/notifications_screen.dart';
import 'package:portraitmode/photo/widgets/featured_photo_list_screen.dart';
import 'package:portraitmode/photo/widgets/upload_photo_modal.dart';
import 'package:portraitmode/search/widgets/search_screen.dart';

class TabsScreen extends ConsumerStatefulWidget {
  const TabsScreen({
    super.key,
    this.clearStates = false,
    this.initialIndex,
    this.searchTabInitialIndex,
    this.customScreen = "",
  });

  final bool clearStates;
  final int? initialIndex;
  final int? searchTabInitialIndex;
  final String customScreen;

  @override
  TabsScreenState createState() => TabsScreenState();
}

class TabsScreenState extends ConsumerState<TabsScreen> {
  List _tappedTabIndexes = [];
  late List<Widget> _tabScreens;
  late List<BottomNavigationBarItem> _navbarItems;
  late int _activeTabMenuIndex;
  late int _activeTabContentIndex;
  late Function? _refreshHomeScreen;
  late Function? _refreshSearchScreen;
  late Function? _refreshFeaturedScreen;

  @override
  void initState() {
    super.initState();

    if (widget.clearStates) {
      // This might lead to bug(s).
      // clearPhotoProviders();
      // clearArtistProviders();
    }

    _tabScreens = [
      HomeScreen(
        onInit: (refreshScreen) {
          _handleScreenInit('ExploreScreen', refreshScreen);
        },
      ),
      SearchScreen(
        onInit: (refreshScreen) {
          _handleScreenInit('SearchScreen', refreshScreen);
        },
      ),
      (widget.customScreen.isNotEmpty
          ? _buildCustomScreen()
          : const SizedBox.shrink()),
      FeaturedPhotoListScreen(
        onInit: (refreshScreen) {
          _handleScreenInit('FeaturedScreen', refreshScreen);
        },
      ),
      const SizedBox.shrink(),
    ];

    _navbarItems = <BottomNavigationBarItem>[
      const BottomNavigationBarItem(
        icon: Icon(Ionicons.home_outline),
        label: "",
      ),
      const BottomNavigationBarItem(
        icon: Icon(Ionicons.search_outline),
        label: "",
      ),
      const BottomNavigationBarItem(
        icon: Icon(Ionicons.add_circle, size: 30.0),
        label: "",
      ),
      const BottomNavigationBarItem(
        icon: Icon(Ionicons.trophy_outline),
        label: "",
      ),
      const BottomNavigationBarItem(
        icon: SizedBox(width: 40.0, height: 34.0, child: NotifIcon()),
        label: "",
      ),
    ];

    _activeTabMenuIndex = widget.initialIndex ?? 0;
    _activeTabContentIndex = _activeTabMenuIndex;
  }

  @override
  Widget build(BuildContext context) {
    _navbarItems[2] = BottomNavigationBarItem(
      icon: Icon(
        Ionicons.add_circle,
        size: 30.0,
        color: context.colors.brandColor,
      ),
      label: "",
    );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          FocusScope.of(context).unfocus();
        }

        if (_activeTabMenuIndex != 0) {
          if (mounted) {
            setState(() {
              _activeTabMenuIndex = 0;
              _activeTabContentIndex = 0;
            });
          }

          return;
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: IndexedStack(
            index: _activeTabContentIndex,
            children: _tabScreens,
          ),
        ),
        bottomNavigationBar: SafeArea(
          child: Container(
            height: LayoutConfig.bottomNavBarHeight,
            decoration: BoxDecoration(
              color: context.colors.scaffoldColor,
              border: Border(
                top: BorderSide(color: context.colors.borderColor, width: 0.7),
              ),
            ),
            child: MaxWidth(
              maxWidth: ScreenStyleConfig.maxWidth,
              child: BottomNavigationBar(
                iconSize: 25.0,
                items: _navbarItems,
                currentIndex: _activeTabMenuIndex,
                selectedFontSize: 12,
                unselectedFontSize: 12,
                onTap: (int index) {
                  _handleNavItemTap(index);
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleNavItemTap(int index) async {
    if (mounted) {
      FocusScope.of(context).unfocus();
    }

    if (index == _activeTabMenuIndex) {
      if (index == 0 && _refreshHomeScreen != null) {
        await _refreshHomeScreen!();
      } else if (index == 1 && _refreshSearchScreen != null) {
        await _refreshSearchScreen!();
      } else if (index == 3 && _refreshFeaturedScreen != null) {
        await _refreshFeaturedScreen!();
      }

      return;
    }

    if (index == 2 || index == 4) {
      if (index == 2) {
        _showUploadForm();
        return;
      } else if (index == 4) {
        _gotoNotificationsScreen(LocalUserService.userId ?? 0);
      }

      return;
    }

    if (mounted) {
      setState(() {
        // Check if index is already tapped.
        if (!_tappedTabIndexes.contains(index)) {
          // Create new _tappedTabIndexes list with the new index.
          _tappedTabIndexes = [..._tappedTabIndexes, index];
        }

        _activeTabMenuIndex = index;
        _activeTabContentIndex = index;
      });
    }
  }

  void _handleScreenInit(String screenName, Function fn) {
    if (screenName == 'ExploreScreen') {
      _refreshHomeScreen = fn;
    } else if (screenName == 'SearchScreen') {
      _refreshSearchScreen = fn;
    } else if (screenName == 'FeaturedScreen') {
      _refreshFeaturedScreen = fn;
    }
  }

  void _gotoNotificationsScreen(int profileId) {
    NotificationUtil().markAsRead(ref);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NotificationsScreen(profileId: profileId),
      ),
    );
  }

  void _showUploadForm() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.9,
          initialChildSize: 0.6,
          expand: false,
          builder: ((BuildContext context, ScrollController scrollController) {
            return UploadPhotoModal(scrollController: scrollController);
          }),
        );
      },
    );
  }

  Widget _buildCustomScreen() {
    return const SizedBox.shrink();
  }
}
