import 'package:flutter/material.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/image_pinch_zooming/image_pinch_zooming.dart';

class PhotoImage extends StatelessWidget {
  const PhotoImage({
    super.key,
    required this.photoUrl,
    this.aspectRatio,
    this.width,
    this.height,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.zoomable = false,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onZoomStart,
    this.onZoomEnd,
  });

  final String photoUrl;
  final double? aspectRatio;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final bool zoomable;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;
  final VoidCallback? onZoomStart;
  final VoidCallback? onZoomEnd;

  @override
  Widget build(BuildContext context) {
    if (aspectRatio != null) {
      return AspectRatio(
        aspectRatio: aspectRatio!,
        child: zoomable ? _buildZoomablePhoto() : _buildUnzoomablePhoto(),
      );
    }

    return zoomable ? _buildZoomablePhoto() : _buildUnzoomablePhoto();
  }

  Widget _buildZoomablePhoto() {
    return ImagePinchZooming(
      image: _buildPhoto(),
      // hideStatusBarWhileZooming: true,
      onTap: onTap,
      onDoubleTap: onDoubleTap,
      onLongPress: onLongPress,
      onTwoFingersOn: onTwoFingersOn,
      onTwoFingersOff: onTwoFingersOff,
      onZoomStart: onZoomStart,
      onZoomEnd: onZoomEnd,
    );
  }

  Widget _buildUnzoomablePhoto() {
    return GestureDetector(
      onTap: onTap,
      onDoubleTap: onDoubleTap,
      onLongPress: onLongPress,
      child: _buildPhoto(),
    );
  }

  Widget _buildPhoto() {
    return PmNetworkImage(
      url: photoUrl,
      width: width,
      height: height,
      alignment: const Alignment(-1.0, -1.0),
    );
  }
}
