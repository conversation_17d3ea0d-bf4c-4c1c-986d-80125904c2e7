// Core packages.
import 'package:flutter/material.dart';
// Extension packages.
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_field_validator/form_field_validator.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/album/http_responses/album_response.dart';
import 'package:portraitmode/album/services/album_service.dart';
// Internal packages.
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class EditAlbumModal extends ConsumerStatefulWidget {
  const EditAlbumModal({super.key, required this.album});

  final AlbumData album;

  @override
  EditAlbumModalState createState() => EditAlbumModalState();
}

class EditAlbumModalState extends ConsumerState<EditAlbumModal> {
  final _scrollController = ScrollController();
  final _formKey = GlobalKey<FormState>();
  final _albumNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _albumNameController.text = widget.album.text;
    _albumNameController.selection = TextSelection(
      baseOffset: 0,
      extentOffset: _albumNameController.text.length,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _albumNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 8.0,
        bottom: MediaQuery.viewInsetsOf(context).bottom,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          controller: _scrollController,
          child: _buildForm(),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              top: 16.0,
              bottom: 16.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: PmTextField(
              controller: _albumNameController,
              autofocus: true,
              labelText: "Album name",
              validator: RequiredValidator(
                errorText: "Album name is required",
              ).call,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8.0,
              bottom: 16.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: SubmitButton(
              buttonText: "Update",
              width: double.infinity,
              height: 40.0,
              fontWeight: FontWeight.w600,
              fontSize: 14.0,
              onPressed: _handleSubmission,
            ),
          ),
        ],
      ),
    );
  }

  final _albumService = AlbumService();

  Future<void> _handleSubmission() async {
    if (!_formKey.currentState!.validate()) return;
    FocusScope.of(context).unfocus();

    AlbumResponse response = await _albumService.edit(
      albumSlug: widget.album.slug,
      albumName: _albumNameController.text,
    );

    if (!response.success || response.data == null) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    ref
        .read(myAlbumProvider.notifier)
        .updateItem(
          AlbumData(
            slug: response.data!.slug,
            text: response.data!.text,
            totalPhotos: response.data!.totalPhotos,
          ),
        );

    if (mounted) {
      Navigator.of(context).pop();

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(response.message)));
    }
  }
}
