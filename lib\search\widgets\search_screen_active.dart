// search_screen_active.dart

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/search/providers/search_artists_data_provider.dart';
import 'package:portraitmode/search/providers/search_artists_provider.dart';
import 'package:portraitmode/search/providers/search_cameras_data_provider.dart';
import 'package:portraitmode/search/providers/search_cameras_provider.dart';
import 'package:portraitmode/search/providers/search_categories_data_provider.dart';
import 'package:portraitmode/search/providers/search_categories_provider.dart';
import 'package:portraitmode/search/providers/search_photos_data_provider.dart';
import 'package:portraitmode/search/providers/search_photos_provider.dart';
import 'package:portraitmode/search/widgets/artists_tab_content.dart';
import 'package:portraitmode/search/widgets/cameras_tab_content.dart';
import 'package:portraitmode/search/widgets/categories_tab_content.dart';
import 'package:portraitmode/search/widgets/photos_tab_content.dart';
import 'package:portraitmode/search/widgets/search_field.dart';
import 'package:portraitmode/search/widgets/search_tabbar_delegate.dart';

class SearchScreenActive extends ConsumerStatefulWidget {
  final int? initialIndex;
  final Function(Function)? onInit;
  final bool searchOnType;
  final String keyword;

  const SearchScreenActive({
    super.key,
    this.initialIndex,
    this.onInit,
    this.searchOnType = false,
    this.keyword = '',
  });

  @override
  SearchScreenActiveState createState() => SearchScreenActiveState();
}

class SearchScreenActiveState extends ConsumerState<SearchScreenActive> {
  final _scrollController = ScrollController();
  final _searchFieldController = TextEditingController();

  late String _keyword;

  final int _counterInterval = 50;
  final int _acceptedWaitingDuration = 500;
  int _waitingDurationCounter = 0;

  Timer? _intervalCounter;
  bool _initialized = false;

  @override
  void initState() {
    _keyword = widget.keyword;
    _searchFieldController.text = _keyword;

    if (widget.onInit != null) {
      widget.onInit!(_scrollToTop);
    }

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchFieldController.dispose();
    if (_intervalCounter != null) _intervalCounter!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final photosSearchData = ref.watch(searchPhotosDataProvider);
    final categoriesSearchData = ref.watch(searchCategoriesDataProvider);
    final camerasSearchData = ref.watch(searchCamerasDataProvider);
    final artistsSearchData = ref.watch(searchArtistsDataProvider);

    List<PhotoData> photoList = ref.watch(searchPhotosProvider);
    List<CategoryData> categoryList = ref.watch(searchCategoriesProvider);
    List<CameraData> cameraList = ref.watch(searchCamerasProvider);
    List<ArtistData> artistList = ref.watch(searchArtistsProvider);

    if (!_initialized) {
      _initialized = true;
    }

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          FocusScope.of(context).unfocus();
        }
      },
      child: DefaultTabController(
        initialIndex: widget.initialIndex ?? 0,
        length: 4,
        child: Scaffold(
          body: SafeArea(
            child: NestedScrollView(
              controller: _scrollController,
              // floatHeaderSlivers: true,
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    automaticallyImplyLeading: true,
                    floating: true,
                    pinned: false,
                    elevation: 0,
                    title: SearchField(
                      controller: _searchFieldController,
                      onChanged: _handleSearchFieldChanged,
                      onSubmitted: _handleSearchSubmit,
                      hintText: 'Search...',
                    ),
                  ),

                  SliverPersistentHeader(
                    floating: false,
                    pinned: true,
                    delegate: SearchTabbarDelegate(),
                  ),
                ];
              },
              body: TabBarView(
                children: [
                  PhotosTabContent(
                    searchData: photosSearchData,
                    keyword: _keyword,
                    dataList: photoList,
                  ),
                  CategoriesTabContent(
                    searchData: categoriesSearchData,
                    keyword: _keyword,
                    dataList: categoryList,
                  ),
                  CamerasTabContent(
                    searchData: camerasSearchData,
                    keyword: _keyword,
                    dataList: cameraList,
                  ),
                  ArtistsTabContent(
                    searchData: artistsSearchData,
                    keyword: _keyword,
                    dataList: artistList,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _scrollToTop() async {
    await _scrollController.animateTo(
      -100.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void _handleSearchFieldChanged(String keyword) {
    if (keyword == _keyword) return;
    _keyword = keyword;

    if (widget.searchOnType) {
      _resetWaitingDuration();
      _setCounter();
    }
  }

  void _setCounter() {
    if (_intervalCounter != null) return;

    _intervalCounter ??= Timer.periodic(
      Duration(milliseconds: _counterInterval),
      (timer) {
        _setWaitingDuration();
      },
    );
  }

  void _setWaitingDuration() {
    _waitingDurationCounter += _counterInterval;

    // log('waitingDurationCounter: $_waitingDurationCounter');

    if (_waitingDurationCounter >= _acceptedWaitingDuration) {
      _resetWaitingDuration();
      _handleSearchSubmit(_keyword);
    }
  }

  void _resetWaitingDuration() {
    _waitingDurationCounter = 0;

    // log('resetting waiting duration');

    if (_intervalCounter != null) {
      _intervalCounter!.cancel();
      _intervalCounter = null;
    }
  }

  void _handleSearchSubmit(String value) {
    _restartPhotoSearch();
    _restartCategoriesSearch();
    _restartCamerasSearch();
    _restartArtistsSearch();
  }

  void _restartPhotoSearch() {
    ref.read(searchPhotosReactiveServiceProvider).clear();
    ref.read(searchPhotosDataProvider.notifier).setLoadMoreLastId(0);
    ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(false);
  }

  void _restartCategoriesSearch() {
    ref.read(searchCategoriesReactiveServiceProvider).clear();
    ref.read(searchCategoriesDataProvider.notifier).setOffset(0);
    ref
        .read(searchCategoriesDataProvider.notifier)
        .setLoadMoreEndReached(false);
  }

  void _restartCamerasSearch() {
    ref.read(searchCamerasReactiveServiceProvider).clear();
    ref.read(searchCamerasDataProvider.notifier).setLastId(null);
    ref.read(searchCamerasDataProvider.notifier).setLoadMoreEndReached(false);
  }

  void _restartArtistsSearch() {
    ref.read(searchArtistsReactiveServiceProvider).clear();
    ref.read(searchArtistsDataProvider.notifier).setLoadMoreLastId(-1);
    ref.read(searchArtistsDataProvider.notifier).setLastTotalPhotos(-1);
    ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(false);
  }
}
