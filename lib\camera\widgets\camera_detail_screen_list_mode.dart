import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/providers/camera_photos_interaction_provider.dart';
import 'package:portraitmode/camera/providers/camera_photos_provider.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/load_more_builder.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CameraDetailScreenListMode extends ConsumerStatefulWidget {
  const CameraDetailScreenListMode({
    super.key,
    required this.camera,
    this.initialScrollIndex = 0,
  });

  final CameraData camera;
  final int initialScrollIndex;

  @override
  CameraDetailScreenListModeState createState() =>
      CameraDetailScreenListModeState();
}

class CameraDetailScreenListModeState
    extends ConsumerState<CameraDetailScreenListMode> {
  final _scrollController = ScrollController();
  final PhotoListService _photoListService = PhotoListService();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  late final int _profileId;

  late int _loadMorePerPage;
  bool _loadMoreEndReached = false;

  @override
  void initState() {
    _loadMorePerPage = LoadMoreConfig.camerasPerPage;
    _profileId = LocalUserService.userId ?? 0;

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _blockScrollNotifier.dispose();
    super.dispose();
  }

  final ValueNotifier<bool> _blockScrollNotifier = ValueNotifier(false);

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    const double appBarHeight = LayoutConfig.bottomNavBarHeight - 10;

    return Scaffold(
      appBar: PmAppBar(
        height: appBarHeight,
        titleText: widget.camera.name,
        useLogo: false,
        automaticallyImplyLeading: true,
        scrollController: _scrollController,
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 768.0),
            child: RefreshIndicator(
              key: _refreshIndicatorKey,
              onRefresh: _handleRefresh,
              child: ValueListenableBuilder(
                valueListenable: _blockScrollNotifier,
                builder: (context, blockScrolling, child) {
                  final photoList = ref.watch(
                    cameraPhotosProvider(widget.camera.slug),
                  );

                  return ScrollablePositionedList.builder(
                    minCacheExtent: _cacheExtent,
                    physics: blockScrolling
                        ? const NeverScrollableScrollPhysics()
                        : null,
                    itemCount: photoList.length,
                    itemBuilder: (BuildContext context, int index) {
                      return LoadMoreBuilder(
                        isFinished: _loadMoreEndReached,
                        onLoadMore: _handleLoadMore,
                        loadingWidgetColor: context.colors.baseColorAlt,
                        idleStatusText: "",
                        loadingStatusText: "",
                        finishedStatusText: "",
                        isLastIndex:
                            (photoList.isEmpty ||
                            index == photoList.length - 1),
                        child: _buildPhotoListItem(photoList[index], index),
                      );
                    },
                    initialScrollIndex: widget.initialScrollIndex,
                    itemScrollController: ItemScrollController(),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoListItem(PhotoData photo, int index) {
    double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
    bool isOwnProfile = photo.authorId == _profileId;

    return Container(
      margin: EdgeInsets.only(top: marginTop),
      child: PhotoListItem(
        index: index,
        photo: photo,
        isOwnProfile: isOwnProfile,
        screenName: 'camera_detail_screen',
        onTwoFingersOn: () {
          _blockScrollNotifier.value = true;
        },
        onTwoFingersOff: () {
          _blockScrollNotifier.value = false;
        },
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref
        .read(cameraPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.camera.slug, 0);

    _loadMoreEndReached = false;

    PhotoListResponse response = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: 0,
      cameraName: widget.camera.name,
    );

    _handlePhotoListResponse(response, true, false);
  }

  Future<bool> _handleLoadMore() async {
    final int loadMoreLastId = ref
        .read(cameraPhotosInteractionProvider.notifier)
        .getLoadMoreLastId(widget.camera.slug);

    final isFirstLoad = loadMoreLastId == 0;

    PhotoListResponse response = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      cameraName: widget.camera.name,
    );

    _handlePhotoListResponse(response, false, isFirstLoad);

    return response.success;
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final cameraPhotosReactiveService = ref.read(
      cameraPhotosReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        cameraPhotosReactiveService.clear();
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(cameraPhotosInteractionProvider.notifier)
        .setLoadMoreLastId(widget.camera.slug, response.data.last.id);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      cameraPhotosReactiveService.replaceAll(widget.camera.slug, response.data);
    } else {
      if (isFirstLoad) {
        cameraPhotosReactiveService.replaceAll(
          widget.camera.slug,
          response.data,
        );
      } else {
        cameraPhotosReactiveService.addItems(widget.camera.slug, response.data);
      }
    }
  }
}
