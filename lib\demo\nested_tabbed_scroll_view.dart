import 'package:flutter/material.dart';

class NestedTabbedScrollView extends StatefulWidget {
  const NestedTabbedScrollView({super.key});

  @override
  State<NestedTabbedScrollView> createState() => _NestedTabbedScrollViewState();
}

class _NestedTabbedScrollViewState extends State<NestedTabbedScrollView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _searchController;

  // Data for each tab
  static const List<String> _tabTitles = <String>[
    'Tab One',
    'Tab Two',
    'Tab Three',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabTitles.length, vsync: this);
    _searchController = TextEditingController();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // Generates dummy list items for a given tab prefix
  List<String> _generateListItems(String prefix, int count) {
    return List<String>.generate(count, (index) => '$prefix Item ${index + 1}');
  }

  @override
  Widget build(BuildContext context) {
    const double searchBarHeight = 60.0;
    const double imageHeight = 150.0; // Height allocated for the image
    const double kToolbarHeight = 56.0; // Default app bar toolbar height

    return Scaffold(
      body: SafeArea(
        child: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              // SliverAppBar for title, image, and search field
              SliverAppBar(
                title: const Text('Nested Scroll Tabs'),
                expandedHeight: kToolbarHeight + imageHeight + searchBarHeight,
                flexibleSpace: FlexibleSpaceBar(
                  background: Image.network(
                    'https://www.gstatic.com/flutter-onestack-prototype/genui/example_1.jpg',
                    fit: BoxFit.cover,
                  ),
                ),
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(searchBarHeight),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 8.0,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.0),
                        boxShadow: [
                          BoxShadow(
                            // Fix: Replace withOpacity with explicit ARGB color to set alpha
                            color: Color.fromARGB((255 * 0.1).toInt(), 0, 0, 0),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          hintText: 'Search...',
                          border: InputBorder.none,
                          prefixIcon: Icon(Icons.search),
                          contentPadding: EdgeInsets.symmetric(vertical: 10.0),
                        ),
                        onChanged: (String value) {
                          // This callback is triggered when the text changes.
                          // You can implement search logic or update a data model here.
                        },
                      ),
                    ),
                  ),
                ),
                // This app bar will scroll away when content is scrolled down.
                floating: true,
                snap: true,
                pinned: false,
              ),
              // SliverAppBar for TabBar, pinned
              SliverAppBar(
                automaticallyImplyLeading:
                    false, // Hide back button for this secondary app bar
                pinned: true, // This app bar stays visible at the top
                primary:
                    false, // Ensure it doesn't take up primary app bar space
                toolbarHeight:
                    0.0, // No toolbar content for this app bar, only the bottom TabBar
                bottom: TabBar(
                  controller: _tabController,
                  tabs: _tabTitles
                      .map<Widget>((String title) => Tab(text: title))
                      .toList(),
                ),
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: _tabTitles.map<Widget>((String title) {
              // Get the first letter of the tab title for unique list content
              final String prefix = title.split(' ')[0];
              final List<String> items = _generateListItems(
                prefix,
                50,
              ); // 50 items per tab

              return ListView.builder(
                padding: EdgeInsets.zero, // Remove default padding
                itemCount: items.length,
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    title: Text(items[index]),
                    leading: CircleAvatar(child: Text('${index + 1}')),
                  );
                },
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}
