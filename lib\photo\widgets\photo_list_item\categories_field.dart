import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/widgets/categories_picker.dart';

class CategoriesField extends StatefulWidget {
  const CategoriesField({super.key});

  @override
  CategoriesFieldState createState() => CategoriesFieldState();
}

class CategoriesFieldState extends State<CategoriesField> {
  List<CategoryData> _selectedCategories = [];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 8.0,
        bottom: 16.0,
        left: ScreenStyleConfig.horizontalPadding,
        right: ScreenStyleConfig.horizontalPadding,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          border: Border.all(color: context.colors.borderColor, width: 1.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => CategoriesPicker(
                      selectedCategories: _selectedCategories,
                      onClose: (cats) {
                        FocusScope.of(context).unfocus();

                        if (mounted) {
                          setState(() {
                            _selectedCategories = cats;
                          });
                        }
                      },
                    ),
                  ),
                );
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    Ionicons.add_circle,
                    color: context.colors.accentColor,
                    size: 20.0,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    "Add categories",
                    style: TextStyle(color: context.colors.accentColor),
                  ),
                ],
              ),
            ),
            if (_selectedCategories.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(
                  left: 4.0,
                  right: 4.0,
                  bottom: 2.0,
                ),
                child: Wrap(
                  spacing: 8.0,
                  runSpacing: -6.0,
                  children: _selectedCategories
                      .map(
                        (cat) => Chip(
                          label: Text(cat.name),
                          onDeleted: () {
                            if (mounted) {
                              setState(() {
                                _selectedCategories.remove(cat);
                              });
                            }
                          },
                        ),
                      )
                      .toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
