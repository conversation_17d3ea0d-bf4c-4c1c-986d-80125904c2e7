import 'package:flutter/material.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/colors.dart';

class AlbumTabbar extends SliverPersistentHeaderDelegate {
  AlbumTabbar({required this.albumList});

  final List<AlbumData> albumList;

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: context.colors.lightColor,
      child: TabBar(
        isScrollable: true,
        padding: EdgeInsets.zero,
        tabAlignment: TabAlignment.start,
        dividerHeight: 1.0,
        dividerColor: context.colors.borderColor,
        indicatorPadding: EdgeInsets.zero,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 2.7, color: context.colors.brandColor),
          borderRadius: BorderRadius.zero, // This removes the rounded corners
        ),
        labelColor: context.isDarkMode
            ? context.colors.brandColor
            : context.colors.brandColorAlt,
        unselectedLabelColor: context.colors.primarySwatch[300],
        // These are the widgets to put in each tab in the tab bar.
        tabs: albumList.map((AlbumData album) {
          return Tab(text: '${album.text} (${album.totalPhotos})');
        }).toList(),
      ),
    );
  }

  @override
  double get maxExtent => 45.0;

  @override
  double get minExtent => 45.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}
