import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/utils/device_util.dart';
import 'package:portraitmode/app/widgets/tabs_screen.dart';
import 'package:portraitmode/auth/dto/auth_data.dart';
import 'package:portraitmode/auth/http_responses/auth_response.dart';
import 'package:portraitmode/auth/services/auth_service.dart';
import 'package:portraitmode/auth/widgets/lost_password_screen.dart';
import 'package:portraitmode/auth/widgets/privacy_policy_link.dart';
import 'package:portraitmode/auth/widgets/register_screen.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/hive/dto/local_auth_data.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  LoginScreenState createState() => LoginScreenState();
}

class LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final authService = AuthService();
  String? error;
  late AuthResponse authResponse;

  final usernameFieldController = TextEditingController();
  final passwordFieldController = TextEditingController();

  @override
  void initState() {
    super.initState();
    LocalAuthService.destroy();
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.sizeOf(context).height;

    return Scaffold(
      backgroundColor: context.colors.lightColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Center(
            child: MaxWidthBuilder(
              maxWidth: 520.0,
              height: screenHeight - MediaQuery.paddingOf(context).top,
              builder: (BuildContext context, BoxConstraints constraints) {
                double containerWidth = constraints.maxWidth;

                return Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Center(
                      child: Form(
                        key: _formKey,
                        child: SizedBox(
                          width: (containerWidth / 100) * 85,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 35.0),
                                child: Image.asset(
                                  context.isDarkMode
                                      ? "assets/logo-white.png"
                                      : "assets/logo.png",
                                  width:
                                      containerWidth - (containerWidth / 3.5),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: 8.0,
                                  bottom: 16.0,
                                ),
                                child: PmTextField(
                                  controller: usernameFieldController,
                                  keyboardType: TextInputType.emailAddress,
                                  labelText: "Email",
                                  validator:
                                      FieldValidators.usernameValidator.call,
                                  onChanged: (String value) {
                                    if (!mounted) return;

                                    setState(() {
                                      error = null;
                                    });
                                  },
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: 8.0,
                                  bottom: 0.0,
                                ),
                                child: PmTextField(
                                  controller: passwordFieldController,
                                  labelText: "Password",
                                  obscureText: true,
                                  validator:
                                      FieldValidators.passwordValidator.call,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: 8.0,
                                  bottom: 25.0,
                                ),
                                child: Align(
                                  alignment: Alignment.centerRight,
                                  child: GestureDetector(
                                    onTap: onForgotPasswordTap,
                                    child: Text(
                                      "Forgot your password?",
                                      style: TextStyle(
                                        color:
                                            context.colors.primarySwatch[400],
                                        fontSize: 13.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 50.0,
                                width: double.infinity,
                                child: SubmitButton(
                                  buttonText: "Login",
                                  onPressed: onFormSubmit,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: 16.0,
                                  bottom: 25.0,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "Don't have an account?",
                                      style: TextStyle(
                                        color:
                                            context.colors.primarySwatch[400],
                                        fontSize: 13.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    const SizedBox(width: 5.0),
                                    GestureDetector(
                                      onTap: onRegisterLinkTap,
                                      child: Text(
                                        "Register",
                                        style: TextStyle(
                                          color: context.colors.accentColor,
                                          fontSize: 13.0,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16.0),
                              error != null
                                  ? Text(
                                      "$error",
                                      style: const TextStyle(color: Colors.red),
                                    )
                                  : const SizedBox.shrink(),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: Platform.isIOS ? 40.0 : 20.0,
                      child: PrivacyPolicyLink(),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    usernameFieldController.dispose();
    passwordFieldController.dispose();
    super.dispose();
  }

  void onForgotPasswordTap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LostPasswordScreen()),
    );
  }

  void onRegisterLinkTap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RegisterScreen()),
    );
  }

  Future<void> onFormSubmit() async {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) {
      return;
    }

    final String deviceModel = await getDeviceModelSlug();

    authResponse = await authService.login(
      username: usernameFieldController.text,
      password: passwordFieldController.text,
      device: deviceModel,
    );

    if (authResponse.success) {
      AuthData data = authResponse.data ?? AuthData();

      await LocalUserService.replace(
        LocalUserData(
          userId: data.id,
          nicename: data.nicename,
          role: data.role,
          displayName: data.displayName,
          profileUrl: data.profileUrl,
          avatarUrl: data.avatarUrl,
          membershipType: data.membershipType,
        ),
      );

      // log('The accessToken after login is: ${data.accessToken}');
      // log('The refreshToken after login is: ${data.refreshToken}');

      await LocalAuthService.replace(
        LocalAuthData(
          isLoggedIn: true,
          accessToken: data.accessToken,
          refreshToken: data.refreshToken,
        ),
      );

      ProfileData profileData = ProfileData(
        id: data.id,
        nicename: data.nicename,
        role: data.role,
        email: data.email,
        website: data.website,
        instagram: data.instagram,
        profileUrl: data.profileUrl,
        avatarUrl: data.avatarUrl,
        firstName: data.firstName,
        lastName: data.lastName,
        displayName: data.displayName,
        description: data.description,
        location: data.location,
        latestPhotoUrl: data.latestPhotoUrl,
        totalPhotos: data.totalPhotos,
        camera: data.camera,
        focalLength: data.focalLength,
        isFollowing: data.isFollowing,
        totalFollowing: data.totalFollowing,
        totalFollowers: data.totalFollowers,
        membershipType: data.membershipType,
      );

      ref.read(profileProvider.notifier).replace(profileData);

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const TabsScreen()),
        );
      }

      return;
    }

    if (mounted) {
      setState(() {
        error = authResponse.message;
      });
    }
  }
}
